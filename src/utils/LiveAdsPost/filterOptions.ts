import { useAppSelector } from "store";

import { filterIsShowOptions } from "utils/options";
import { toDataOptions } from "utils/utils";
import { handleSelectedValue } from "utils/filter";
import { IFilterOptions } from "types/PostService";

import { FormatOptions, POST_FILTER, POST_LABEL, StatusOptions } from "constants/postComment";
import { PlatformOptions } from "constants/LiveAdsPost";
import { onSetParamsChange } from ".";

const filterOptions = ({
  setSelectedOption,
  isCategory,
  isDateTime,
  isStatus,
  isLive,
  isPLatform,
  isFormat,
  params,
}: IFilterOptions & {
  params?: any;
  setSelectedOption: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
}) => {
  const { data } = useAppSelector((state) => state.category);
  return [
    isCategory
      ? {
          isSearchable: true,
          type: "select-multi",
          key: POST_FILTER.CATEGORIES,
          placeholder: POST_LABEL.category__in,
          options: filterIsShowOptions(toDataOptions(data.items, "code", "name")),
          selected: handleSelectedValue(params, POST_FILTER.CATEGORIES),
          onChange: (value: any) =>
            onSetParamsChange({
              key: POST_FILTER.CATEGORIES,
              value,
              setSelectedOption,
            }),
          className:
            "w-full md:w-1/2 lg:w-[250px] !border-custom-primary !rounded-xl !text-custom-tertiary",
          isTruncateLabel: true,
        }
      : null,
    isFormat
      ? {
          type: "select-multi",
          key: POST_FILTER.FORMAT,
          placeholder: POST_LABEL.att_type__in,
          options: FormatOptions,
          selected: handleSelectedValue(params, POST_FILTER.FORMAT),
          onChange: (value: any) =>
            onSetParamsChange({
              key: POST_FILTER.FORMAT,
              value,
              setSelectedOption,
            }),
          className: "w-full md:w-1/2 lg:w-[250px]",
        }
      : null,
    isLive
      ? {
          type: "select-multi",
          key: "is_live",
          isSingle: true,
          placeholder: POST_LABEL.is_active,
          options: StatusOptions,
          selected: handleSelectedValue(params, POST_FILTER.IS_LIVE),
          onChange: (value: any) =>
            onSetParamsChange({
              key: POST_FILTER.IS_LIVE,
              value,
              setSelectedOption,
            }),
          className: "w-full md:w-1/2 lg:w-[200px] !border-custom-primary  text-md !rounded-xl",
        }
      : null,
    isStatus
      ? {
          type: "select-multi",
          isSingle: true,
          key: POST_FILTER.STATUS,
          placeholder: POST_LABEL.is_active,
          options: StatusOptions,
          selected: handleSelectedValue(params, POST_FILTER.STATUS),
          onChange: (value: any) =>
            onSetParamsChange({
              key: POST_FILTER.STATUS,
              value,
              setSelectedOption,
            }),
          className: "w-full md:w-1/2 lg:w-[200px]",
        }
      : null,
    isPLatform
      ? {
          type: "select-multi",
          key: POST_FILTER.PLATFORM,
          placeholder: POST_LABEL.publisher_platform__in,
          options: filterIsShowOptions(PlatformOptions),
          selected: handleSelectedValue(params, POST_FILTER.PLATFORM),
          onChange: (value: any) =>
            onSetParamsChange({
              key: POST_FILTER.PLATFORM,
              value,
              setSelectedOption,
            }),
          className: "w-full md:w-1/2 lg:w-[250px]",
        }
      : null,

    isDateTime
      ? {
          type: "date",
          placeholder: "date",
          onChange: (value: any) =>
            setSelectedOption?.((prev) => ({
              ...prev,
              [POST_FILTER.DATE_FROM]: value.from,
              [POST_FILTER.DATE_TO]: value.to,
            })),
          date_from: params[POST_FILTER.DATE_FROM] || "",
          date_to: params[POST_FILTER.DATE_TO] || "",
          className:
            "p-2 w-full md:w-1/2 lg:w-[200px] custom--dropdown-container !border-custom-primary !rounded-xl h-10",
        }
      : null,
  ];
};

export default filterOptions;
