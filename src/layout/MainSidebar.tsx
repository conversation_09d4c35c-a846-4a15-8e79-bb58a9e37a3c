import { useDispatch, useSelector } from "react-redux";
import { onToggleCollapse, sidebarStore } from "store/redux/sidebar/slice";
import { RiArrowLeftSLine } from "@remixicon/react";

import Logo from "components/Logo";
import NavSection from "./NavSection/NavSection";
import SidebarConfig from "../layout/SidebarConfig";
import UserSidebar from "./UserLayout/UserSidebar";

import { cn } from "utils/utils";
import useResponsive from "hooks/useResponsive";

type Props = {
  className?: string;
  onCloseSidebar?: () => void;
};

const MainSidebar = ({ className, onCloseSidebar }: Props) => {
  const { collapse } = useSelector(sidebarStore);
  const dispatch = useDispatch();
  const { isMobile } = useResponsive();

  return (
    <div
      className={cn(
        "z-50 max-[768px]:h-[calc(100vh-30px)] md:h-screen py-2 border-r",
        isMobile ? "flex" : "fixed",
        className
      )}
    >
      {!isMobile && (
        <div
          className={cn(
            "absolute z-50 shadow-toggle border-[#919EAB14] top-[20px] w-[26px] h-[26px] border rounded-full flex items-center justify-center bg-white right-[13px] translate-x-full cursor-pointer",
            collapse ? "rotate-180" : "rotate-0"
          )}
          onClick={() => dispatch(onToggleCollapse())}
          children={<RiArrowLeftSLine color="#919EAB" size={16} />}
        />
      )}
      <nav
        className={cn(
          "h-full flex flex-col translate duration-300",
          collapse ? `w-[60px]` : "w-[280px]"
        )}
      >
        <Logo className="w-fit" />
        <div className="flex h-full flex-col w-full overflow-x-hidden">
          <NavSection onCloseSidebar={onCloseSidebar} navConfig={SidebarConfig()} />
          {!isMobile && <UserSidebar />}
        </div>
      </nav>
    </div>
  );
};

export default MainSidebar;
