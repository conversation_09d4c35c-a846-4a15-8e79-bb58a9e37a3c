import { useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { socialStore } from "store/redux/social/slice";
import { sidebarStore } from "store/redux/sidebar/slice";

import { Box } from "components/Box";
import { Toaster } from "components/ui/toaster";
import { useToast } from "components/ui/use-toast";
import FooterWrapper from "./Footer";
import Modal from "components/Modal";
import HeaderMobile from "./HeaderMobile";
import MainSidebar from "./MainSidebar";

import useResponsive from "hooks/useResponsive";
import { cn } from "utils/utils";
import { ChatContainer } from '../components/ChatContainer';
import { IS_DEV } from './SidebarConfig';

const SidebarLayout = () => {
  const { isOpenSearchSuggest } = useSelector(socialStore);
  const { collapse } = useSelector(sidebarStore);
  const { isMobile } = useResponsive();
  const { dismiss } = useToast();
  const location = useLocation();

  // Reset  when route change
  useEffect(() => {
    window.scrollTo(0, 0);
    dismiss();
  }, [location.pathname]);

  const isChatPage = location.pathname.includes("big360-assistant");
  const isHideFooter = !location.pathname.includes("big360-assistant");

  return (
    <div className=" bg-custom-primary h-full">
      <div className={cn(!isMobile && "max-w-[1449px] mx-auto h-full")}>
        <div className="fixed top-0 z-50 w-screen">
          {isMobile ? <HeaderMobile isMobile={isMobile} /> : <MainSidebar />}
        </div>
        <main
          className={cn(
            "rounded-sm flex-1 p-2 h-full translate duration-300 min-h-[700px] mt-[54px] sm:mt-0",
            collapse ? "md:ml-[60px]" : "min-[769px]:ml-[280px]"
          )}
        >
          <Box variant="col-start"
               className={cn('w-full h-fit relative p-2 md:p-6 gap-0', isChatPage ?
                 'h-full overflow-hidden' :
                 'min-h-screen')}
          >
            <div className={cn("flex-1 h-full w-full min-h-[500px]", isChatPage && "flex flex-col")}>
              <Outlet />
            </div>
            {isHideFooter && <FooterWrapper />}
          </Box>
          <Modal />
        </main>
        {isOpenSearchSuggest && <div className="fixed w-full h-full z-[999] bg-[#00000054]"></div>}
      </div>
      <Toaster />
      {IS_DEV && <div className="fixed bottom-[24px] right-[24px] z-[999]">
        <ChatContainer />
      </div>}
    </div>
  );
};

export default SidebarLayout;
