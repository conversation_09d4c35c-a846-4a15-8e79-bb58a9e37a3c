import { useState } from "react";
import { RiErrorWarningLine, RiEyeLine, RiEyeOffLine } from "@remixicon/react";
import { Control, Controller } from "react-hook-form";

import LabelAuth from "./LabelAuth";

import { cn } from "utils/utils";

type Props = {
  control: Control<any>;
  defaultValue?: string;
  placeholder?: string;
  name: string;
  type?: "text" | "password" | "number";
  required?: boolean;
  label?: string;
  error?: string;
  className?: string;
};

const InputController = (props: Props) => {
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Controller
      name={props.name}
      control={props.control}
      defaultValue={props.defaultValue || ""}
      rules={{ required: props.required, minLength: 6 }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={cn("flex flex-col gap-1 ", props.className)}>
          {props.label && <LabelAuth text={props.label} required={props.required} />}
          <div className="w-full bg-white flex items-center relative h-full">
            <input
              className={cn(
                "p-3 text-tertiary border border-custom-primary w-full rounded-xl focus:outline-none h-full",
                `${props.error ? "border-error border" : "focus:border-focus focus:shadow-focus"}`
              )}
              placeholder={props.placeholder}
              value={value || ""}
              defaultValue={props.defaultValue}
              onChange={onChange}
              onBlur={onBlur}
              type={props.type === "password" && !showPassword ? "password" : "text"}
            />
            {props.type === "password" && (
              <div className="absolute top-0 right-0 p-[1px] h-full w-8">
                <div className="w-full h-full bg-white rounded-r-xl flex items-center justify-center">
                  <button type="button" onClick={togglePasswordVisibility}>
                    {showPassword ? (
                      <RiEyeOffLine color="#0D1126" opacity={0.4} size={20} />
                    ) : (
                      <RiEyeLine color="#0D1126" opacity={0.4} size={20} />
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
          {props.error && (
            <div className="text-red-500 text-xs flex items-center gap-1 lowercase">
              <RiErrorWarningLine size={16} color="#F48E2F99" opacity={0.6} /> {props.error}
            </div>
          )}
        </div>
      )}
    />
  );
};

export default InputController;
