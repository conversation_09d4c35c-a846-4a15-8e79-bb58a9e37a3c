import { useRef, useState } from 'react';
import * as HoverCard from '@radix-ui/react-hover-card';

import { Badge } from 'components/ui/badge';

import useOutsideClick from 'hooks/useClickOutSide';
import { TSelectOptionCheckbox } from 'types/Select';

import { cn } from 'utils/utils';
import SelectGroups from 'components/FilterOptions/SelectGroups';
import { RiCloseLine, RiLockLine } from '@remixicon/react';
import { Box } from 'components/Box';

interface Props {
  label: string;
  icon?: React.ReactNode;
  activeCount?: number;
  activeValue?: {[key: string]: string};
  groupKey?: number;
  groupSelect?: TSelectOptionCheckbox[];
  onClickChangeTab?: any;
  isEnable?: boolean;
  onTagRemove:()=>void
  isSegmentDetail?:boolean
}

const FilterInterestAudience = ({
  label,
  icon,
  groupKey,
  groupSelect,
  activeCount,
  activeValue,
  onClickChangeTab,
  isEnable = false,
  onTagRemove,
  isSegmentDetail = false
}: Props) => {
  const ref = useRef(null);
  const refSecond = useRef(null);

  const [open, setOpen] = useState<boolean>(false);

  useOutsideClick(ref, refSecond, () => {
    setOpen(false);
  });

  const handleOnClick = () => {
    setOpen((prev) => !prev);
    onClickChangeTab && onClickChangeTab();
  };
  const isActiveValue = activeValue && Object.keys(activeValue).length > 0;
  return (
    <div className='relative'>
      {isActiveValue && <span onClick={onTagRemove} className="cursor-pointer dropdown-tag-close absolute left-4 top-1/2 transform -translate-y-1/2 z-10">

        {isEnable &&  <RiCloseLine className="" size={14} color="#5314a3" />}
      </span>}

      <HoverCard.Root open={open} openDelay={300} closeDelay={300}>
        <HoverCard.Trigger
          ref={ref}
          asChild
          className={cn(
            'custom--dropdown-container px-2.5 py-[5px] rounded-xl border min-h-8 h-10 border-primary !box-border relative min-w-auto',
            !isEnable && ' text-infor-disable bg-[#0A0F290A] !cursor-default'
          )}
          onClick={() => ( isEnable ? handleOnClick() : null )}
        >
          <Box className={cn('gap-1 relative w-[170px]', isEnable ? 'justify-end' : 'justify-start')}>
            {icon ? <span>{icon}</span> : undefined}
            <div
              className={cn('absolute left-1 transform text-sm text-[#82868b] bg-white z-10 opacity-100 transition-all flex top-1/2 translate-x-2 -translate-y-1/2', open || activeValue || ( activeCount && activeCount > 0 ) ?
                '-top-[5px]' :
                'duration-300 ',!isActiveValue?"!bg-transparent":"")}
            >
              {label}
            </div>

            {isActiveValue ? <>
              <Badge
                className={cn('rounded-[4px] bg-brand-light hover:bg-brand-light text-[#5314a3] pr-2', isEnable ?
                  ' pl-6 ' :
                  ' pl-1')}
              >
                <p className="truncate w-[80px]">
                  {activeValue.category}
                </p>
              </Badge>
            </> : undefined}
            {activeCount && activeCount > 0 && (
              <Badge className="rounded-md bg-brand-light hover:bg-brand-light text-[#5314a3] px-2">
                {activeCount}
              </Badge>
            )}
            {!isEnable && !isSegmentDetail && <RiLockLine size={17} />}
            {isEnable && <IconOpen isOpen={open} />}
          </Box>
        </HoverCard.Trigger>
        <HoverCard.Portal>
          <HoverCard.Content
            sideOffset={12}
            align="end"
            className="border-none rounded-md bg-white z-50"
            ref={refSecond}
          >
            {groupSelect && (
              <SelectGroups placeholder={label} groupSelect={groupSelect} key={groupKey} />
            )}
          </HoverCard.Content>
        </HoverCard.Portal>
      </HoverCard.Root>
    </div>
  );
};

export default FilterInterestAudience;

const IconOpen = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" className='ml-auto' fill="none">
      <path
        d="M7.99999 8.78114L11.3 5.48114L12.2427 6.42381L7.99999 10.6665L3.75732 6.42381L4.69999 5.48114L7.99999 8.78114Z"
        fill="#0F1324"
        fillOpacity={isOpen ? "1" : "0.6"}
      />
    </svg>
  );
};
