import { useEffect, useRef, useState } from "react";
import { RiCloseLine, RiSearchLine } from "@remixicon/react";

import { Box } from "./Box";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { TSelectOption } from "types/Select";
import { cn, removeVietnameseTones } from "utils/utils";
import useOutsideClick from "hooks/useClickOutSide";
import { TooltipPortal } from '@radix-ui/react-tooltip';

interface Props {
  placeholder?: string;
  options: TSelectOption[];
  isMulti?: boolean;
  isSearchable?: boolean;
  onChange?: any;
  align?: string;
  defaultValue?: any;
  className?: string;
  itemShow?: number;
  isDisabled?: boolean;
  isTruncateLabel?: boolean;
}

const IconOpen = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M7.99999 8.78114L11.3 5.48114L12.2427 6.42381L7.99999 10.6665L3.75732 6.42381L4.69999 5.48114L7.99999 8.78114Z"
        fill="#0F1324"
        fillOpacity={isOpen ? "1" : "0.6"}
      />
    </svg>
  );
};
const MultiSelect = ({
  placeholder,
  options,
  isMulti,
  isSearchable,
  onChange,
  align,
  defaultValue,
  className,
  itemShow = 1,
  isDisabled = false,
  isTruncateLabel = false,
}: Props) => {
  const [showMenu, setShowMenu] = useState(false);
  const [selectedValue, setSelectedValue] = useState<any>(isMulti ? defaultValue || [] : []);
  const [searchValue, setSearchValue] = useState<any>("");
  const inputRef = useRef<any>(null);
  const menuRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    setSearchValue("");
    if (showMenu && searchRef.current) {
      searchRef.current.focus();
    }
  }, [showMenu]);

  useOutsideClick(inputRef, menuRef, () => {
    setShowMenu(false);
  });

  const handleInputClick = () => {
    if (isDisabled) return;
    setShowMenu(!showMenu);
  };

  const getDisplay = () => {
    if (!selectedValue || selectedValue.length === 0) {
      return showMenu ? "" : placeholder;
    }
    const renderTags = () => {
      if (selectedValue.length > itemShow) {
        const remainingCount = selectedValue.length - itemShow;
        return (
          <div className="dropdown-tags">
            {selectedValue.slice(0, itemShow).map((option: any, index: number) => (
              <div
                key={`${option?.value}-${index}`}
                className={cn("dropdown-tag-item", isTruncateLabel && "line-clamp-1 w-fit")}
              >
                <span
                  className={cn(
                    "leading-5",
                    isTruncateLabel &&
                      "overflow-hidden text-ellipsis w-fit whitespace-nowrap max-w-[50px] inline-block"
                  )}
                >
                  {option.label}
                </span>

                <span onClick={(e) => onTagRemove(e, option)} className="dropdown-tag-close">
                  <RiCloseLine className="" size={14} color="#5314a3" />
                </span>
              </div>
            ))}
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="dropdown-tag-item">+{remainingCount}</div>
                </TooltipTrigger>
               <TooltipPortal>
                 <TooltipContent side="top" align="start">
                   <div className="dropdown-tags max-w-[300px]">
                     {selectedValue.slice(itemShow).map((option: any, index: number) => (
                       <div key={`${option?.value}-${index}`} className="dropdown-tag-item">
                         {option.label}
                         <span
                           onClick={(e) => onTagRemove(e, option)}
                           className="dropdown-tag-close"
                         >
                          <RiCloseLine className="" size={14} color="#5314a3" />
                        </span>
                       </div>
                     ))}
                   </div>
                 </TooltipContent>
               </TooltipPortal>
              </Tooltip>
            </TooltipProvider>
          </div>
        );
      } else {
        return (
          <div className="dropdown-tags">
            {selectedValue.map((option: any, index: number) => (
              <div key={`${option?.value}-${index}`} className="dropdown-tag-item">
                <span
                  className={cn(
                    "leading-5",
                    isTruncateLabel &&
                      "overflow-hidden text-ellipsis w-fit whitespace-nowrap max-w-[100px] inline-block"
                  )}
                >
                  {option.label}
                </span>

                <span onClick={(e) => onTagRemove(e, option)} className="dropdown-tag-close">
                  <RiCloseLine className="" size={14} color="#5314a3" />
                </span>
              </div>
            ))}
          </div>
        );
      }
    };
    return renderTags();
  };

  const removeOption = (option: any) => {
    return selectedValue.filter((o: any) => o.value !== option.value);
  };

  const onTagRemove = (e: any, option: any) => {
    e.stopPropagation();
    const newValue = removeOption(option);
    setSelectedValue(newValue);
    onChange(newValue);
  };
  const onItemClick = (option: any) => {
    let newValue;

    if (isMulti) {
      const isSelected = selectedValue.some((o: any) => o.value === option.value);

      if (isSelected) {
        newValue = selectedValue.filter((o: any) => o.value !== option.value);
      } else {
        newValue = [...selectedValue, option];
      }
    } else {
      newValue = [option];
      setShowMenu(false);
    }

    setSelectedValue(newValue);
    onChange(newValue);
  };

  const isSelected = (option: any) => {
    if (isMulti) {
      return selectedValue.filter((o: any) => o.value === option.value).length > 0;
    }
    if (!selectedValue) {
      return false;
    }
    return selectedValue.value === option.value;
  };
  const onSearch = (e: any) => {
    setSearchValue(e.target.value);
  };
  const getOptions = () => {
    if (!searchValue) {
      return options;
    }
    return options.filter((option: any) => {
      const target = removeVietnameseTones(option.label).toLocaleLowerCase();
      return target.indexOf(removeVietnameseTones(searchValue.toLowerCase())) >= 0;
    });
  };

  const showPlaceholderTag = showMenu || (selectedValue && selectedValue.length > 0);

  return (
    <div
      className={cn(
        "custom--dropdown-container min-h-8 h-10 border-primary !box-border relative",
        showMenu ? "min-w-[150px]" : "min-w-auto",
        className,
        isDisabled && "bg-[#0A0F290A] [&_svg]:hidden !cursor-default"
      )}
    >
      <div ref={inputRef} onClick={handleInputClick} className={cn("dropdown-input")}>
        <div
          className={cn(
            "absolute translate-x-2 -translate-y-1/2 text-custom-tertiary text-xs",
            showPlaceholderTag
              ? "-top-[5px] left-1 bg-white z-10 opacity-100 transition-all duration-500"
              : "top-1/2 left-1 opacity-0 "
          )}
        >
          {placeholder}
        </div>
        <div
          className={`dropdown-selected-value  ${
            !selectedValue || selectedValue.length === 0 ? "placeholder" : ""
          }`}
        >
          {getDisplay()}
        </div>
        <div className="dropdown-tools">
          <div className="dropdown-tool">
            <IconOpen isOpen={showMenu} />
          </div>
        </div>
      </div>
      {showMenu && (
        <div
          className={`dropdown-menu alignment--${
            align || "auto"
          } !z-[99] bottom-0 translate-y-[105%]`}
          ref={menuRef}
        >
          {isSearchable && (
            <Box className="search-box border rounded-lg gap-1 p-0">
              <RiSearchLine size={16} />
              <input
                className="form-control outline-none p-0"
                onChange={onSearch}
                value={searchValue}
                ref={searchRef}
                placeholder="Search..."
              />
            </Box>
          )}
          {getOptions().map((option: any) => (
            <div
              onClick={() => onItemClick(option)}
              key={option.value}
              className={`dropdown-item ${isSelected(option) && "selected"}`}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
export default MultiSelect;
