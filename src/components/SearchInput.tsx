import { RiSearchLine } from "@remixicon/react";
import { useEffect, useState } from "react";
import { cn } from "utils/utils";

interface Props {
  placeholder?: string;
  searchTerm?: string;
  className?: string;
  disabled?: boolean;
  onChange: (value: string) => void;
}
const SearchInput = ({
  placeholder = "search ....",
  searchTerm = "",
  className = "",
  disabled,
  onChange,
}: Props) => {
  const [search, setSearch] = useState<string>("");

  const handleChange = (value: string) => {
    onChange(value);
  };

  const handleOnKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleChange(search);
    }
  };

  useEffect(() => {
    setSearch(searchTerm);
  }, [searchTerm]);

  return (
    <div className="w-full">
      <div
        className={cn(
          "border flex items-center w-full py-1 px-4 shadow-sm mt-3 rounded-md",
          className,
          disabled && "opacity-30"
        )}
      >
        <input
          className="focus:outline-none placeholder:text-secondary text-primary w-full text-sm "
          placeholder={placeholder}
          onChange={(e) => setSearch(e.target.value)}
          onKeyUp={(e) => handleOnKeyUp(e)}
          defaultValue={search}
          value={search}
          autoFocus
          disabled={disabled}
        />
        <RiSearchLine
          className="cursor-pointer"
          color="#0F132499"
          size={20}
          onClick={() => handleChange(search)}
        />
      </div>
    </div>
  );
};

export default SearchInput;
