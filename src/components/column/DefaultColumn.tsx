import { cn } from '../../utils/utils.ts';
import { TooltipWrapper } from '../TooltipWrapper';

const DefaultColumn = ({ value, isLowercase, className }: any) => {
  return (
    <TooltipWrapper
      trigger={
        <span className={cn('py-2 overflow-hidden text-ellipsis w-fit whitespace-nowrap md:max-w-[100px]  lg:max-w-[150px] xl:max-w-[350px] inline-block', className)}>{String(value) === 'null' || '' ?
          '-' :
          isLowercase ? String(value).toLowerCase() : String(value)}
        </span>
      }
      delayDuration={300}
      side="top"
      sideOffset={-40}
      className="z-30"
    >
      <span className="py-2 ">{String(value) === 'null' || '' ?
        '-' :
        isLowercase ? String(value).toLowerCase() : String(value)}</span>
    </TooltipWrapper>
  );
};
export default DefaultColumn;
