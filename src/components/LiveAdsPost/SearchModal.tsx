import { LegacyRef, useRef, useState } from "react";
import { RiCloseLine, RiSearchLine } from "@remixicon/react";

import useOutsideClick from "hooks/useClickOutSide";
import useFilters from "hooks/useFilters";
import { handleSearch } from "utils/LiveAdsPost";
import { cn } from "utils/utils";
import { useAppSelector } from '../../store';

type Props = {
  value: string;
  placeholder?: string;
  className?: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  SearchSuggest?: ({
    value,
    refSecond,
    handleSearch,
  }: {
    value: string;
    refSecond: LegacyRef<HTMLDivElement> | undefined;
    handleSearch: () => void;
  }) => JSX.Element;
};

const SearchModal = ({ value, placeholder, className, SearchSuggest, setSearchTerm }: Props) => {
  const ref = useRef<HTMLInputElement | null>(null);
  const refSecond = useRef(null);
  const { params, setSearchParams } = useFilters();
  const { user } = useAppSelector((state) => state.auth);

  const [searchSuggestModal, setSearchSuggestModal] = useState({
    data: [],
    isOpen: false,
  });
  const [isComposing, setIsComposing] = useState(false);

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = (e: React.CompositionEvent<HTMLInputElement>) => {
    setIsComposing(false);
    // Cập nhật lại giá trị sau khi kết thúc composition
    setSearchTerm(e.currentTarget.value);
  };

  useOutsideClick(ref, refSecond, () => {
    handleCloseModal();
  });
  const handleOpenModal = () => {
    setSearchSuggestModal((prev) => ({ ...prev, isOpen: true }));
  };
  const handleCloseModal = () => {
    setSearchSuggestModal((prev) => ({ ...prev, isOpen: false }));
  };
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !isComposing) {
      handleSearch({ params, searchTerm: value, setSearchTerm, setSearchParams, user_id:user.uuid });
      value !== "" && handleCloseModal();
    }
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (searchSuggestModal.isOpen == false) {
      handleOpenModal();
    }
  };

  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          "p-2 flex items-center gap-2 border border-custom-primary rounded-xl text-custom-tertiary text-sm h-10",
          searchSuggestModal.isOpen && "bg-custom-secondary"
        )}
      >
        <RiSearchLine
          className="cursor-pointer"
          size={16}
          onClick={() =>
            handleSearch({ params, searchTerm: value, setSearchTerm, setSearchParams, user_id:user.uuid })
          }
        />

        <input
          ref={ref}
          value={value}
          onClick={() => handleOpenModal()}
          onChange={(e) => handleChange(e)}
          onKeyDown={(e) => handleKeyDown(e)}
          onCompositionStart={handleCompositionStart}
          onCompositionEnd={handleCompositionEnd}
          placeholder={placeholder}
          className={cn(
            "w-full font-light bg-transparent focus:outline-none text-custom-tertiary placeholder:text-custom-tertiary",
            searchSuggestModal.isOpen && "text-secondary"
          )}
        />
        {value && (
          <RiCloseLine
            className="cursor-pointer hover:opacity-75"
            size={16}
            onClick={() => setSearchTerm("")}
          />
        )}
      </div>

      {searchSuggestModal.isOpen && SearchSuggest && (
        <SearchSuggest
          refSecond={refSecond}
          value={value}
          handleSearch={() => {
            handleSearch({ params, searchTerm: value, setSearchTerm, setSearchParams, user_id:user.uuid }),
              handleCloseModal();
          }}
        />
      )}
    </div>
  );
};

export default SearchModal;
