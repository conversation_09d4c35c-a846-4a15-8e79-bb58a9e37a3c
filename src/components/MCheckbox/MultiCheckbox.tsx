import React, { useEffect, useState } from "react";
import { styled } from "styled-components";

import TreeItem from "./TreeItem";

import { TSelectOption, TSelectOptionCheckbox } from "types/Select";
import {
  collectSelectedNodes,
  flattenTree,
  updateNodeStatus,
  updateParentStatus,
} from "utils/MCheckbox";
import { cn, removeVietnameseTones } from "utils/utils";
import { RiSearchLine } from "@remixicon/react";

const MultiCheckbox = ({
  options,
  isSearchable = false,
  isEnable = true,
  isSingleSelect = false,
  onChange,
  searchCallback,
  handleSetSearchKeyword,
  children,
  className
}: TSelectOptionCheckbox) => {
  const [nodes, setNodes] = useState(options);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filteredNodes, setFilteredNodes] = useState<TSelectOption[]>(options);
  
  useEffect(() => {
    setNodes(options);
    setFilteredNodes(options);
  }, [options]);

  const handleBoxChecked = (event: React.ChangeEvent<HTMLInputElement>, node: TSelectOption) => {
    const { name, checked } = event.target;
    node.checked = checked;

    //reset checked when the checkbox is checked
    if (isSingleSelect) {
      nodes.map((item) => (item.checked = false));
    }

    const newNodes = updateNodeStatus({ nodeList: nodes, name, checked: isSingleSelect ? true : checked });
    updateParentStatus(newNodes); //update parent when all child checked
    const selectedNodes = collectSelectedNodes(newNodes);

    //update state
    setNodes(newNodes);
    onChange && onChange(node, selectedNodes);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (searchCallback) {
      const newFilteredNodes = searchCallback(nodes, term);
      setFilteredNodes(newFilteredNodes);
    } else {
      const lowerCaseSearchTerm = removeVietnameseTones(term).toLowerCase();
      const newFilteredNodes = flattenTree(nodes).filter((node) =>
        removeVietnameseTones(node.label).toLowerCase().includes(lowerCaseSearchTerm)
      );
      setFilteredNodes(newFilteredNodes);
    }
  };

  const handleOnKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSetSearchKeyword && handleSetSearchKeyword(searchTerm);
    }
  };

  return (
    <DropdownWrapper
      className={cn(
        "text-sm",
        isEnable ? "text-secondary " : "pointer-events-none !text-infor-disable"
      )}
    >
      {isSearchable && (
        <div className="border flex items-center w-full pr-4 shadow-[0px_1px_2px_0px_#14151A0D] mt-2 rounded-md focus:outline-green-500">
          <SearchWrapper
            type="text"
            placeholder="Search..."
            className="outline-none"
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            onKeyUp={(e) => handleOnKeyUp(e)}
          />
          {handleSetSearchKeyword && (
            <RiSearchLine
              className="cursor-pointer"
              color="#0F132499"
              size={20}
              onClick={() => handleSetSearchKeyword(searchTerm)}
            />
          )}
        </div>
      )}
      {children}
      {filteredNodes.length === 0 && <div className="px-2 py-5 text-center"><p>No Results Found</p></div>}
      <DropdownContent isSearchable={isSearchable} className={className}>
        {filteredNodes.map((node, index) => (
          <TreeItem node={{ ...node }} onBoxChecked={handleBoxChecked} key={index} isSingleSelect={isSingleSelect}/>
        ))}
      </DropdownContent>
    </DropdownWrapper>
  );
};

export default MultiCheckbox;

const DropdownWrapper = styled("div")(() => ({
  position: "relative",
  padding: "4px",
  zIndex: "1",
  background: "transparent",
  border: "none",
  boxShadow: "none",
  height: "100%",
}));

const DropdownContent = styled("ul")<{ isSearchable: boolean }>(({ isSearchable }) => ({
  width: "100%",
  height: "100%",
  maxHeight: isSearchable ? "245px" : "272px",
  overflowY: "scroll",
  marginTop: "4px",
}));

const SearchWrapper = styled("input")(() => ({
  padding: "5px 0 5px 1rem",
  width: "100%",
  borderRadius: "8px",
  color: "#0f132499",
}));
