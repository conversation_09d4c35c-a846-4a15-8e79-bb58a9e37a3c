import { Controller } from "react-hook-form";
import { cn } from "utils/utils";

type Props = {
  label: string;
  control: any;
  name: string;
  maxLength?: number;
  defaultValue?: string;
  className?: string;
};

const TextAreaController = ({
  name,
  control,
  label,
  maxLength,
  defaultValue,
  className,
}: Props) => {
  return (
    <Controller
      render={({ field }) => (
        <div className={cn("text-sm relative flex flex-col gap-2", className)}>
          <label className=" font-normal text-secondary">{label}</label>
          <textarea
            maxLength={maxLength || 50}
            rows={5}
            {...field}
            defaultValue={defaultValue}
            className="mt-1 py-1.5 px-2 block w-full resize-none border focus:outline-none focus:shadow-focus focus:border-focus border-gray-300 rounded-xl text-tertiary"
          ></textarea>
          <div className="absolute bottom-2 right-2 text-[#0D112666] text-[10px]">
            {field.value?.length || 0}/{maxLength || 50}
          </div>
        </div>
      )}
      control={control}
      name={name}
    />
  );
};

export default TextAreaController;
