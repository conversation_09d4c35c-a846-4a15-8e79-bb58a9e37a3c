import { Navigate, createBrowserRouter } from "react-router-dom";
import NotFound from "views/NotFound";
import PrivateRouter from "./PrivateRouter";

import useLiveAnalyzeRouter from "./useLiveAnalyzeRouter";
import useAdsPostAnalyzeRouter from "./useAdsPostAnalyzeRouter";
import useRequestAudienceRouter from "./useRequestAudienceRouter";
import useAuthentication from "./useAuthenticationRouter";
import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";
import usePersonaRouter from "./usePersonaRouter";
import useSocialRouter from "./useSocialRouter";
import useEnrichmentRouter from "./useEnrichmentRouter";
import useDataProcessingRouter from "./useDataProcessingRouter";
import useYourAudienceRouter from "./useYourAudienceRouter";
import useYourSegmentsRouter from "./useYourSegmentsRouter";
import useCRM360Router from "./useCRM360Router";
import useUserProfileRouter from "./useUserProfileRouter";
import LandingLayout from "layout/LandingLayout";
import PaymentStatus from "views/UserBillingView/components/PaymentStatus";
import useAiAgentRouter from './useAiAgentRouter';

// ----------------------------------------------------------------------
const MainLayout = Loadable(lazyWithRetry(() => import("layout/index")));
// const DashboardView = Loadable(lazyWithRetry(() => import("views/Dashboard/index")));
const PlanAndPaymentView = Loadable(lazyWithRetry(() => import("views/PlanAndPaymentView/index")));
const IS_DEV = import.meta.env.REACT_APP_DEPLOY_TAG === 'development';

const router = createBrowserRouter([
  {
    path: "/",
    element: <MainLayout />,
    children: [
      {
        path: "",

        element: <PrivateRouter children={<Navigate to="/social-data" replace />} />,
      },
      // {
      //   path: "/dashboard",
      //   element: <PrivateRouter children={<DashboardView />} />,
      // },
      //Plan
      {
        path: "/plan/upgrade",
        element: <PrivateRouter children={<PlanAndPaymentView />} />,
      },
      ...useCRM360Router(),
      ...usePersonaRouter(),
      ...useSocialRouter(),
      //crawl data
      ...useLiveAnalyzeRouter(),
      ...useAdsPostAnalyzeRouter(),
      ...useRequestAudienceRouter(),
      //your data
      ...useYourAudienceRouter(),
      ...useYourSegmentsRouter(),
      ...useDataProcessingRouter(),
      //enrichment
      ...useEnrichmentRouter(),
      // ai agent
      ...(IS_DEV ? useAiAgentRouter() : []),
      //user
      ...useUserProfileRouter(),
    ],
  },
  {
    path: "account/billing/success",
    element: <LandingLayout />,
    children: [
      {
        path: "",
        element: (
          <PrivateRouter>
            <PaymentStatus status="SUCCESS" />
          </PrivateRouter>
        ),
      },
    ],
  },
  {
    path: "account/billing/failed",
    element: <LandingLayout />,
    children: [
      {
        path: "",
        element: (
          <PrivateRouter>
            <PaymentStatus status="FAIL" />
          </PrivateRouter>
        ),
      },
    ],
  },
  {
    path: "*",
    element: <LandingLayout hideNav={true}/>,
    children: [
      {
        path: "404",
        element: <NotFound />,
      },
      {
        path: "*",
        element: <NotFound />,
      },
    ],
  },
  useAuthentication(),
]);

export default router;
