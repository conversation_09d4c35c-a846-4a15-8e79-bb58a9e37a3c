import { Ri<PERSON>lose<PERSON><PERSON>, Ri<PERSON><PERSON>der2<PERSON><PERSON>, RiMapPinLine, RiSearchLine } from "@remixicon/react";
import NotFoundLocation from "assets/icons/NotFoundLocation";
import { useEffect, useRef, useState } from "react";
import { LocationData } from "types/map";
import { cn } from "utils/utils";
import { useMapContext } from "views/Persona/context/MapProvider";

type Props = {
  onChange: (value: string) => void;
  searchResult?: LocationData[];
  loading?: boolean;
  error?: string | null;
  onRemoveText: () => void;
};

const SearchMap: React.FC<Props> = ({ onChange, searchResult, loading, error, onRemoveText }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const { setMapCenter, mapCenter } = useMapContext();
  const [searchInput, setSearchInput] = useState<{
    value: string;
    loader: boolean;
    location?: [number, number];
  }>({
    value: "",
    loader: false,
  });
  const [isOpen, setIsOpen] = useState<boolean>(false);

  function toggle(e: MouseEvent) {
    setIsOpen(e && e.target === inputRef.current);
  }
  useEffect(() => {
    if (!searchInput.location) return;
    if (searchInput.location[0] !== mapCenter[0] && searchInput.location[1] !== mapCenter[1]) {
      setSearchInput({
        value: "",
        loader: false,
      });
      onChange("");
    }
  }, [mapCenter, searchInput.location]);
  useEffect(() => {
    document.addEventListener("click", toggle);
    return () => document.removeEventListener("click", toggle);
  }, []);
  useEffect(() => {
    if (!searchInput.loader) return;
    const handleSubmit = async () => {
      onChange(searchInput.value);
    };
    const timeout = setTimeout(() => {
      handleSubmit();
    }, 500);
    return () => clearTimeout(timeout);
  }, [searchInput]);

  return (
    <div className="relative">
      <div
        className={cn(
          "flex relative items-center border h-[40px] rounded-xl px-3 py-2",
          isOpen && "border-b-0 rounded-b-none"
        )}
      >
        <input
          ref={inputRef}
          value={searchInput.value}
          onChange={(e) =>
            setSearchInput({
              value: e.target.value,
              loader: true,
            })
          }
          className={"flex-1 outline-none text-sm font-medium"}
          placeholder="Search location"
        />
        {!loading ? (
          searchInput.value.trim().length > 0 ? (
            <button
              onClick={() => {
                setSearchInput({
                  value: "",
                  loader: true,
                });
                onRemoveText();
              }}
            >
              <RiCloseLine size={20} />
            </button>
          ) : (
            <RiSearchLine size={20} />
          )
        ) : (
          <RiLoader2Line className="animate-spin" size={20} />
        )}
      </div>
      {isOpen && searchResult && (
        <div className="absolute top-full min-h-20 right-0 p-2 left-0 rounded-md rounded-t-none border-t-0 border bg-white shadow-sm max-h-44 w-full overflow-auto z-[9999]">
          <div className="flex flex-col gap-1">
            {searchResult.map((item) => (
              <button
                onClick={() => {
                  setMapCenter([+item.lat, +item.lon]);
                  setSearchInput({
                    value: item.display_name,
                    loader: false,
                    location: [+item.lat, +item.lon],
                  });
                }}
                key={item.place_id}
                className="p-1 text-xs flex rounded-sm items-center justify-start gap-2 w-full bg-gray-100 hover:bg-gray-200 line-clamp-1"
              >
                <RiMapPinLine className="flex-shrink-0" size={14} />
                <span className="line-clamp-1 flex-1 text-start font-medium">
                  {item.display_name}
                </span>
              </button>
            ))}
          </div>
          {error ? (
            <div className="text-xs my-auto text-secondary font-medium flex items-center gap-2">
              <NotFoundLocation color={""} size={14} />
              Not found location
            </div>
          ) : (
            searchResult.length === 0 && (
              <div className="text-xs flex items-center gap-2 font-medium text-secondary">
                <RiSearchLine size={14} /> Search for location
              </div>
            )
          )}
        </div>
      )}
    </div>
  );
};
export default SearchMap;
