import { useCallback, useEffect, useRef, useState } from "react";
import { Ri<PERSON><PERSON><PERSON>ill, RiSearchLine } from "@remixicon/react";
import { SetURLSearchParams, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from "react-redux";
import { onCloseSearchSuggest, onOpenSearchSuggest, socialStore } from "store/redux/social/slice";

import { Badge } from "components/ui/badge";
import SearchSuggest from "./SearchSuggest";
import { ParamsProps } from "views/SocialDataView";

import { socialAPI } from "apis/socialData";
import useDebounce from "hooks/useDebounce";
import useOutsideClick from "hooks/useClickOutSide";
import { cn } from "utils/utils";
import { SOCIAL_DATA_LABEL } from "constants/socialData/label";
import { useAppSelector } from '../../../../store';
import { tracking } from '../../../../utils/Tracking/tracking';
import { useAbortController } from '../../../../hooks/useAbortController';

const SearchModal = ({
  params,
  setSearchParams,
}: {
  params: ParamsProps;
  setSearchParams: SetURLSearchParams;
  keywordSuggest?: boolean;
}) => {
  const dispatch = useDispatch();
  const queryTerm = params?.q || "";
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchSuggest, setSearchSuggest] = useState<any>([]);
  const { isOpenSearchSuggest } = useSelector(socialStore);
  const { newAbortController } = useAbortController();
  const { user } = useAppSelector((state) => state.auth);
  const location = useLocation();

  const ref = useRef<HTMLInputElement | null>(null);
  const refSecond = useRef(null);

  useEffect(() => {
    setSearchTerm(queryTerm);
  }, [queryTerm]);

  const debounceSearch = useDebounce(searchTerm, 500);

  const handleAction = () => {
    setSearchParams({ ...params, page: "1", q: searchTerm });

    //remove focus input
    if (ref.current) {
      ref.current.blur();
    }
    //close search suggests
    dispatch(onCloseSearchSuggest());
    tracking({
      eventName: 'search_data',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          search_term: searchTerm,
          path: { ...params,pathName: location.pathname, page: "1", q: searchTerm }
        })
      }
    });
  };

  const handleOnkeyUpEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.keyCode === 13) {
      handleAction();
    }
  };
  const onReset = () => {
    setSearchParams((prev) => ({ ...prev, q: "" }));
    setSearchTerm("");
  };

  const onFocus = () => {
    dispatch(onOpenSearchSuggest());
    if (searchTerm == "") {
      fetchDataSuggest("");
    }
  };

  useOutsideClick(ref, refSecond, () => {
    dispatch(onCloseSearchSuggest());
  });

  const fetchDataSuggest = useCallback(
    async (q: string) => {
      const controller = newAbortController();
      const res = await socialAPI.get({
        endpoint: "search-suggest/",
        params: { q },
        signal: controller.signal
      });

      if (res.data) {
        setSearchSuggest(res.data.items);
      }
    },
    [debounceSearch]
  );

  useEffect(() => {
    fetchDataSuggest(debounceSearch.trim());
  }, [debounceSearch]);

  return (
    <div
      className={cn("relative mt-3 sm:mt-6", isOpenSearchSuggest && "bg-white z-[9999] rounded-xl")}
    >
      <div
        className={cn(
          "border border-[#DEE0E3] rounded-[12px] flex items-center focus:shadow-focus focus:border-focus",
          isOpenSearchSuggest && "border border-[#924FE8]"
        )}
      >
        <div className="flex-1 px-[12px] py-[10px] flex items-center gap-2">
          <span className="cursor-pointer" onClick={() => handleAction()}>
            <RiSearchLine opacity={0.4} color="#0F1324" size={16} />
          </span>
          <input
            className="w-full font-light bg-transparent focus:outline-none text-sm text-primary"
            placeholder={SOCIAL_DATA_LABEL.placeholder}
            onKeyUp={(e) => handleOnkeyUpEnter(e)}
            onChange={(event) => setSearchTerm(event.target.value)}
            onFocus={() => onFocus()}
            value={searchTerm || ""}
            ref={ref}
          />
          {searchTerm && (
            <div className="flex items-center gap-1">
              <Badge
                className="bg-transparent hover:bg-transparent cursor-pointer"
                onClick={onReset}
              >
                <RiCloseFill color="#14151A" />
              </Badge>
            </div>
          )}
        </div>
      </div>
      {isOpenSearchSuggest && <SearchSuggest suggestData={searchSuggest} refSecond={refSecond} />}
    </div>
  );
};

export default SearchModal;
