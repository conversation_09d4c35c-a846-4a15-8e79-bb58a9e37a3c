import { useContext } from "react";
import { RiFacebookCircleFill, Ri<PERSON><PERSON>der<PERSON>ine, RiSearchLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";
import { RequestAudienceContext } from "../RequestContext";

import { Card } from "components/ui/card";
import { Box } from "components/Box";
import RequestPreview from "./RequestPreview";
import CreateRequestData from "./CreateRequestData";
import NoticeMessage from "./NoticeMessage";
import NoticeLimitRequest from "../../../components/Transaction/NoticeLimitRequest";

import usePreviewFB from "hooks/usePreviewFB";
import handleCloseModal from "utils/handleCloseModal";
import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import useResponsive from "hooks/useResponsive.tsx";
import { But<PERSON> } from "components/ui/button.tsx";
import { RequestInformation } from "./RequestInformation.tsx";

const SearchPreviewURL = () => {
  const context = useContext(ModalContext);
  const onRefreshTable = useContext(RequestAudienceContext)?.getData;

  const usage_limit = useFeatures(FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE)?.usage_limit;
  const used_count = useFeatures(FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE)?.usage_count || 0;
  const { requestURL, requestData, setRequestData, setRequestURL, handleGetPreview } = usePreviewFB(
    {
      type: "request"
    }
  );

  const { isMobile } = useResponsive();

  const handleSearch = async () => {
    const data = await handleGetPreview();
    data?.data &&
      context?.setDataDialog((prev) => ({
        ...prev,
        isOpen: true,
        isShowTitle: false,
        content: <RequestPreview data={data.data} />,
        footer: <CreateRequestData data={data.data} url={data.url} onRefresh={onRefreshTable} />,
        className: "bg-secondary gap-4 lg:max-w-[1000px] p-4",
        handleCancel: () => {
          setRequestData({ data: undefined, loading: false });
          handleCloseModal(context);
        },
      }));
  };


  const handlePopup = () => {
    context?.setDataDialog((prev) => ( {
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <RequestInformation/>,
      className: "max-w-[345px] p-4 gap-2 rounded-2xl",
      handleCancel: () => {
        setRequestData({ data: undefined, loading: false });
        handleCloseModal(context);
      }
    } ));
  };

  return (
    <div>
      <Card className="p-0 md:p-4 mt-1 md:mt-6 mb-3 shadow-none md:shadow-sm border border-secondary rounded-2xl">
        <Box variant="col-start" className="gap-2">
          <Box className="gap-0 p-0 bg-white rounded-xl w-full h-[36px] md:h-[60px] shadow-sm">
            <Box className="px-3 py-[10px] text-secondary font-semibold gap-2 text-sm bg-secondary rounded-tl-xl rounded-bl-xl border-r border-secondary h-full">
              <RiFacebookCircleFill size={20} />
              <span children="Facebook" className="hidden md:block" />
            </Box>
            <Box className="px-3 flex-1 w-full h-full">
              <input
                className="w-full font-medium bg-transparent focus:outline-none text-md text-secondary placeholder:text-tertiary placeholder:text-sm md:placeholder:text-md"
                placeholder={REQUEST_AUDIENCE_LABEL.placeholder}
                onChange={(e) => setRequestURL(e.target.value)}
                onKeyUp={(e) => e.key === "Enter" && handleSearch()}
                disabled={requestData.loading ? true : false}
                value={requestURL}
              />
              {requestData.loading ? (
                <RiLoaderLine className="animate-spin" />
              ) : (
                <RiSearchLine
                  size={24}
                  className="fill-icon-primary cursor-pointer"
                  onClick={() => handleSearch()}
                />
              )}
            </Box>
          </Box>
          {isMobile ? <Button
            variant={'link'}
            className='p-0 h-auto underline text-secondary'
            onClick={() => handlePopup()}
          >
            Informations
          </Button> : <NoticeMessage />}

        </Box>
      </Card>
      <NoticeLimitRequest
        denominator={( usage_limit as number ) || 0}
        used_count={used_count || 0}
        message={
          <div>
            You used
            <strong className="px-1">
              {used_count !== 0 && used_count > ( usage_limit as number ) ? usage_limit : used_count}/
              {usage_limit + " "}
              free
            </strong>
            imports of Facebook URLs.
          </div>
        }
      />
    </div>
  );
};

export default SearchPreviewURL;
