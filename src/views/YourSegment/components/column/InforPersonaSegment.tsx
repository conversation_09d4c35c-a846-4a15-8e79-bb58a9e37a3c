import { Link } from 'react-router-dom';

import AvatarByName from 'components/AvatarByName';
import { YOUR_SEGMENTS } from 'types/Router';
import { TooltipWrapper } from 'components/TooltipWrapper';

const InfoPersonaSegment = ({ id, name }: {id: string | number; name: string}) => {
  return (
    <TooltipWrapper
      trigger={<div className="max-w-[350px] w-[350px] line-clamp-1">
        <Link
          to={`/${YOUR_SEGMENTS.ROOT}/${YOUR_SEGMENTS.PERSONA}/${id}`}
          className="text-sm hover:text-primary-hover items-center gap-2 flex"
        >
          <AvatarByName name={name} position="first" />
          <span className="w-full block truncate">{name}</span>
        </Link>
      </div>}
      delayDuration={100}
      side="top"
      sideOffset={-60}
    >
      <span className="p-2">{name}</span>
    </TooltipWrapper>
  );
};

export default InfoPersonaSegment;
