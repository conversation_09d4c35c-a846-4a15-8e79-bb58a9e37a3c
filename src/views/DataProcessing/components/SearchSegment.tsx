import { RiSearchLine } from "@remixicon/react";
import { Box } from "components/Box";
import { DATAPROCESSING_LABEL } from "constants/yourData/label";

interface ISetSearchValue {
  setSearchValue: React.Dispatch<React.SetStateAction<string>>;
}
const SearchSegment = ({ setSearchValue }: ISetSearchValue) => {
  return (
    <Box className="w-full border rounded-xl p-2 gap-1 text-tertiary text-sm font-normal">
      <RiSearchLine size={16} />
      <input
        className="focus:outline-none placeholder:text-tertiary  w-full"
        placeholder={DATAPROCESSING_LABEL.placeholder_search}
        onChange={(e) => setSearchValue(e.target.value)}
      />
    </Box>
  );
};

export default SearchSegment;
