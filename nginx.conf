gzip on;
gzip_comp_level 6;
gzip_vary on;
gzip_types text/plain text/css application/json application/x-javascript application/javascript text/xml application/xml application/rss+xml text/javascript image/svg+xml application/vnd.ms-fontobject application/x-font-ttf font/opentype;

server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # Handle SPA routing - serve index.html for all routes
    # Cache for 1 hour
    location / {
        try_files $uri $uri/ /index.html;

        # Cache for 1 hour (3600 seconds)
        add_header Cache-Control "public, max-age=3600" always;

        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, Content-Type, Accept";
    }

    # ssl_certificate /etc/nginx/file_chain.crt;
    # ssl_certificate_key /etc/nginx/file_private.key;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

